import HeroSection from './components/HeroSection';
import TripsSection from './components/TripsSection';
import ThemeToggle from './components/ThemeToggle';

export default function Home() {
  return (
    <main className="min-h-screen">
      {/* Theme Toggle */}
      <ThemeToggle />

      {/* Hero Section */}
      <HeroSection />

      {/* Trips Section */}
      <TripsSection />

      {/* Footer */}
      <footer className="bg-gray-800 dark:bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">ბუნებაში გასვლა</h3>
              <p className="text-gray-300">
                პარანორმალური მოვლენების მკვლევარი და ბუნებაში გასვლების ორგანიზატორი
              </p>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">კონტაქტი</h4>
              <div className="space-y-2 text-gray-300">
                <p>📧 <EMAIL></p>
                <p>📱 +995 555 123 456</p>
                <p>📍 თბილისი, საქართველო</p>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">გამოგვყევით</h4>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-300 hover:text-white transition-colors">
                  Facebook
                </a>
                <a href="#" className="text-gray-300 hover:text-white transition-colors">
                  YouTube
                </a>
                <a href="#" className="text-gray-300 hover:text-white transition-colors">
                  Instagram
                </a>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 ბუნებაში გასვლა. ყველა უფლება დაცულია.</p>
          </div>
        </div>
      </footer>
    </main>
  );
}
