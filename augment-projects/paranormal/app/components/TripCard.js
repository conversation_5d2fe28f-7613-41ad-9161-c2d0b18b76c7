'use client';

import Image from 'next/image';
import { useState } from 'react';
import RegistrationModal from './RegistrationModal';
import TripGallery from './TripGallery';

export default function TripCard({ trip }) {
  const [isHovered, setIsHovered] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isGalleryOpen, setIsGalleryOpen] = useState(false);

  const getStatusColor = (status) => {
    switch (status) {
      case 'open':
        return 'bg-green-500';
      case 'filling':
        return 'bg-yellow-500';
      case 'full':
        return 'bg-orange-500';
      case 'completed':
        return 'bg-blue-500';
      case 'closed':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'open':
        return 'ღია რეგისტრაცია';
      case 'filling':
        return 'ივსება';
      case 'full':
        return 'სავსეა';
      case 'completed':
        return 'დასრულებული';
      case 'closed':
        return 'დახურული';
      default:
        return 'უცნობი';
    }
  };

  const progressPercentage = (trip.currentParticipants / trip.maxParticipants) * 100;

  return (
    <div 
      className={`bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transition-all duration-300 transform ${
        isHovered ? 'scale-105 shadow-2xl' : ''
      } ${trip.status === 'completed' ? 'cursor-pointer' : ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Card Header with Image */}
      <div className="relative h-48 overflow-hidden">
        <Image
          src={trip.image}
          alt={trip.title}
          fill
          className="object-cover transition-transform duration-300 hover:scale-110"
        />
        
        {/* Participants Counter */}
        <div className="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-semibold">
          {trip.currentParticipants}/{trip.maxParticipants}
        </div>

        {/* Status Badge */}
        <div className={`absolute top-4 left-4 ${getStatusColor(trip.status)} text-white px-3 py-1 rounded-full text-sm font-semibold`}>
          {getStatusText(trip.status)}
        </div>
      </div>

      {/* Card Content */}
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-2">
          {trip.title}
        </h3>
        
        <p className="text-gray-600 dark:text-gray-300 mb-4 text-sm">
          {trip.description}
        </p>

        {/* Date and Location */}
        <div className="flex items-center gap-4 mb-4 text-sm text-gray-500 dark:text-gray-400">
          <div className="flex items-center gap-1">
            <span>📅</span>
            <span>{trip.date}</span>
          </div>
          <div className="flex items-center gap-1">
            <span>📍</span>
            <span>{trip.location}</span>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              მონაწილეობა
            </span>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {Math.round(progressPercentage)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-500 ${getStatusColor(trip.status)}`}
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Price */}
        <div className="flex items-center justify-between mb-4">
          <span className="text-lg font-bold text-primary dark:text-primary">
            {trip.price} ₾
          </span>
          {trip.status === 'completed' && (
            <span className="text-sm text-blue-600 dark:text-blue-400 font-medium">
              ფოტო/ვიდეო ხელმისაწვდომია
            </span>
          )}
        </div>

        {/* Action Button */}
        <button
          onClick={() => {
            if (trip.status === 'open' || trip.status === 'filling') {
              setIsModalOpen(true);
            } else if (trip.status === 'completed') {
              setIsGalleryOpen(true);
            }
          }}
          className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-300 ${
            trip.status === 'open' || trip.status === 'filling'
              ? 'bg-primary hover:bg-primary-dark text-white transform hover:scale-105'
              : trip.status === 'completed'
              ? 'bg-blue-500 hover:bg-blue-600 text-white transform hover:scale-105'
              : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
          }`}
          disabled={trip.status === 'full' || trip.status === 'closed'}
        >
          {trip.status === 'open' || trip.status === 'filling'
            ? 'რეგისტრაცია'
            : trip.status === 'completed'
            ? 'ფოტო/ვიდეოს ნახვა'
            : trip.status === 'full'
            ? 'სავსეა'
            : 'დახურული'
          }
        </button>
      </div>

      {/* Registration Modal */}
      <RegistrationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        trip={trip}
      />

      {/* Trip Gallery */}
      <TripGallery
        isOpen={isGalleryOpen}
        onClose={() => setIsGalleryOpen(false)}
        trip={trip}
      />
    </div>
  );
}
